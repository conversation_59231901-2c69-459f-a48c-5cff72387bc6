/**
 * 通用选择管理Hook
 * 管理表格行选择状态，支持跨页面选择，支持泛型
 */

import { useState, useCallback, useEffect } from 'react';
import type { TableRowSelection } from 'antd/es/table/interface';

export interface SelectionState<T> {
  selectedRowKeys: React.Key[];
  selectedRows: T[];
}

export interface UseSelectionOptions<T> {
  // 是否传递初始化数据
  initialSelectedKeys?: React.Key[];
  initialSelectedRows?: T[];
  // 当atd table发生变化时，触发这个回调方法，用于同步父组件某些操作
  onSelectionChange?: (selectedKeys: React.Key[], selectedRows: T[]) => void;
}

export interface UseSelectionReturn<T> {
  /** 表格行选择配置 */
  rowSelectionAntdTable: TableRowSelection<T>;
  /** 清空选择方法 */
  clearSelection: () => void;
  /** 选择所有方法 */
  selectAll: (data: T[]) => void;
  /** 取消选择所有方法 */
  unselectAll: (data: T[]) => void;
  /** 获取选中的行数据方法 */
  getSelectedRows: () => T[];
  /** 获取选中的行键方法 */
  getSelectedKeys: () => React.Key[];
  /** 获取选中数量方法 */
  getSelectedCount: () => number;
}

export function useSelectionCrossPage<T extends { id: number | string }>(data: T[], options: UseSelectionOptions<T> = {}): UseSelectionReturn<T> {
  const { onSelectionChange, initialSelectedRows } = options;

  const [isInit, setInit] = useState(false);

  // 当前页面选择状态
  const [selectRows, setSelectRows] = useState<Map<React.Key, T>>(new Map());

  // 跨页面选择状态（所有选中的行数据）
  const [allSelectedRows, setAllSelectedRows] = useState<Map<React.Key, T>>(new Map());

  // 静态方法：使用data更新selectedRows
  const updateSelectedRows = useCallback((data: T[], selectedRows: T[]) => {
    // 更新跨页
    setAllSelectedRows(prev => {
      const newAllSelectedRows = new Map(prev);

      // 移除当前页面所有数据的选择状态
      data.forEach(item => {
        newAllSelectedRows.delete(item.id);
      });

      // 添加新选中的数据
      selectedRows.forEach(row => {
        newAllSelectedRows.set(row.id, row);
      });

      return newAllSelectedRows;
    });

    // 更新当前页面
    setSelectRows(prev => {
      const newMap = new Map(prev);

      // 移除当前页面所有数据的选择状态
      data.forEach(item => {
        newMap.delete(item.id);
      });

      // 添加新选中的数据
      selectedRows.forEach(row => {
        newMap.set(row.id, row);
      });

      return newMap;
    });
  }, []);

  // 如果存在initialSelectedRows：初始化initialSelectedKeys多选key
  useEffect(() => {
    if (initialSelectedRows && initialSelectedRows.length > 0 && !isInit) {
      updateSelectedRows([], initialSelectedRows);
      setInit(true);
    }
  }, [initialSelectedRows, isInit, updateSelectedRows]);

  // 监听多行数据变化
  useEffect(() => {
    // 触发回调 - 在状态更新函数内部调用，确保使用最新状态
    if (onSelectionChange) {
      // 跨页面选择时，传递所有选中的数据
      const allKeys = Array.from(allSelectedRows.keys());
      const allRows = Array.from(allSelectedRows.values());
      console.log('触发回调:', { allKeys, allRows });

      onSelectionChange(allKeys, allRows);
    }
  }, [allSelectedRows]);

  // 处理选择变化
  const handleSelectionChange = useCallback(
    (selectedRowKeys: React.Key[], selectedRows: T[]) => {
      console.log('useSelectionCrossPage 选择变化:', { selectedRowKeys, selectedRows });
      // 跨页面模式：更新选择状态
      updateSelectedRows(data, selectedRows);
    },
    [data, updateSelectedRows] // 移除 allSelectedRows 依赖
  );

  // 清空选择
  const clearSelection = useCallback(() => {
    setSelectRows(new Map());
    setAllSelectedRows(new Map());
    // 立即触发选择变化回调
    if (onSelectionChange) {
      onSelectionChange([], []);
    }
  }, [onSelectionChange]);

  // 选择所有
  const selectAll = useCallback(
    (dataToSelect: T[]) => {
      const keys = dataToSelect.map(item => item.id);
      handleSelectionChange(keys, dataToSelect);
    },
    [handleSelectionChange]
  );

  // 取消选择所有
  const unselectAll = useCallback((dataToUnselect: T[]) => {
    // 跨页面模式：从全局选择中移除指定数据
    setAllSelectedRows(prev => {
      const newMap = new Map(prev);
      dataToUnselect.forEach(item => {
        newMap.delete(item.id);
      });
      return newMap;
    });
  }, []);

  // 获取跨页选中的行数据
  const getSelectedRows = useCallback((): T[] => {
    return Array.from(allSelectedRows.values());
  }, [allSelectedRows]);

  // 获取跨页选中的行键
  const getSelectedKeys = useCallback((): React.Key[] => {
    return Array.from(allSelectedRows.keys());
  }, [allSelectedRows]);

  // 获取跨页选中数量
  const getSelectedCount = useCallback((): number => {
    // console.log('getSelectedCount:', allSelectCount);
    return allSelectedRows.size;
  }, [allSelectedRows.size]);

  // 当前页面
  const getCurrentPageSelectedKeys = useCallback((): React.Key[] => {
    // console.log('getCurrentPageSelectedKeys:', Array.from(selectRows.keys()));
    return Array.from(selectRows.keys());
  }, [selectRows]);

  // Antd Table 表格行选择配置
  const rowSelectionAntdTable: TableRowSelection<T> = {
    type: 'checkbox',
    selectedRowKeys: getCurrentPageSelectedKeys(),
    onChange: handleSelectionChange,
    onSelect: (record, selected, selectedRows, nativeEvent) => {
      console.log('单行选择:', { record, selected, selectedRows, nativeEvent });
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      console.log('全选/取消全选:', { selected, selectedRows, changeRows });
    },
    getCheckboxProps: record => ({
      name: `checkbox-${record.id}`,
    }),
  };

  // 处理初始选择的keys和外部selectedKeys变化
  // useEffect(() => {
  //   if (initialSelectedKeys.length > 0 && data.length > 0) {
  //     const selectedRowsFromKeys = data.filter(item => initialSelectedKeys.includes(item.id));

  //     if (crossPage) {
  //       // 跨页面模式：只更新当前页面的数据到全局选择状态，不清除其他页面的选择
  //       setAllSelectedRows(prev => {
  //         const newMap = new Map(prev);

  //         // 先移除当前页面的所有数据
  //         data.forEach(item => {
  //           newMap.delete(item.id);
  //         });

  //         // 再添加应该选中的数据
  //         selectedRowsFromKeys.forEach(row => {
  //           newMap.set(row.id, row);
  //         });

  //         return newMap;
  //       });
  //     }

  //     // 更新当前页面选择状态
  //     const currentPageSelectedKeys = selectedRowsFromKeys.map(item => item.id);
  //     setSelection({
  //       selectedRowKeys: currentPageSelectedKeys,
  //       selectedRows: selectedRowsFromKeys,
  //     });
  //   } else if (initialSelectedKeys.length === 0) {
  //     // 如果initialSelectedKeys为空，只清空当前页面的选择状态
  //     if (crossPage) {
  //       setAllSelectedRows(prev => {
  //         const newMap = new Map(prev);
  //         // 只移除当前页面的数据，保留其他页面的选择
  //         data.forEach(item => {
  //           newMap.delete(item.id);
  //         });
  //         return newMap;
  //       });
  //     }
  //     setSelection({
  //       selectedRowKeys: [],
  //       selectedRows: [],
  //     });
  //   }
  // }, [initialSelectedKeys, data, crossPage]);

  // 当数据变化时，更新当前页面的选择状态
  // useEffect(() => {
  //   if (crossPage) {
  //     // 跨页面模式：计算当前页面中哪些行应该被选中
  //     const currentPageSelectedKeys: React.Key[] = [];
  //     const currentPageSelectedRows: T[] = [];

  //     data.forEach(item => {
  //       if (allSelectedRows.has(item.id)) {
  //         currentPageSelectedKeys.push(item.id);
  //         currentPageSelectedRows.push(item);
  //       }
  //     });

  //     setSelection({
  //       selectedRowKeys: currentPageSelectedKeys,
  //       selectedRows: currentPageSelectedRows,
  //     });
  //   } else {
  //     // 非跨页面模式：清空不存在于当前数据中的选择项
  //     const currentDataIds = new Set(data.map(item => item.id));
  //     const validSelectedKeys = selection.selectedRowKeys.filter(key => currentDataIds.has(key));
  //     const validSelectedRows = selection.selectedRows.filter(row => currentDataIds.has(row.id));

  //     if (validSelectedKeys.length !== selection.selectedRowKeys.length) {
  //       setSelection({
  //         selectedRowKeys: validSelectedKeys,
  //         selectedRows: validSelectedRows,
  //       });
  //     }
  //   }
  // }, [data, crossPage, allSelectedRows, selection.selectedRowKeys, selection.selectedRows]);

  return {
    rowSelectionAntdTable,
    clearSelection,
    selectAll,
    unselectAll,
    getSelectedRows,
    getSelectedKeys,
    getSelectedCount,
  };
}

export function useSelectionInPage<T extends { id: number | string }>(data: T[], options: UseSelectionOptions<T> = {}): UseSelectionReturn<T> {
  const { initialSelectedKeys = [], onSelectionChange } = options;

  // 当前页面选择状态
  const [selection, setSelection] = useState<SelectionState<T>>({
    selectedRowKeys: [],
    selectedRows: [],
  });

  // 跨页面选择状态（所有选中的行数据）
  // const [allSelectedRows, setAllSelectedRows] = useState<Map<React.Key, T>>(new Map());

  // 处理选择变化
  const handleSelectionChange = useCallback(
    (selectedRowKeys: React.Key[], selectedRows: T[]) => {
      console.log('选择变化:', { selectedRowKeys, selectedRows });

      // 更新当前页面选择状态
      setSelection({
        selectedRowKeys,
        selectedRows,
      });

      // 触发回调
      if (onSelectionChange) {
        // 单页面选择时，只传递当前页面的数据
        onSelectionChange(selectedRowKeys, selectedRows);
      }
    },
    [onSelectionChange]
  );

  // 清空选择
  const clearSelection = useCallback(() => {
    setSelection({
      selectedRowKeys: [],
      selectedRows: [],
    });

    // 立即触发选择变化回调
    if (onSelectionChange) {
      onSelectionChange([], []);
    }
  }, [onSelectionChange]);

  // 选择所有
  const selectAll = useCallback(
    (dataToSelect: T[]) => {
      const keys = dataToSelect.map(item => item.id);
      handleSelectionChange(keys, dataToSelect);
    },
    [handleSelectionChange]
  );

  // 取消选择所有
  const unselectAll = () => {
    // 清空当前页面选择
    setSelection({
      selectedRowKeys: [],
      selectedRows: [],
    });
  };

  // 获取选中的行数据
  const getSelectedRows = useCallback((): T[] => {
    return selection.selectedRows;
  }, [selection.selectedRows]);

  // 获取选中的行键
  const getSelectedKeys = useCallback((): React.Key[] => {
    return selection.selectedRowKeys;
  }, [selection.selectedRowKeys]);

  // 获取选中数量
  const getSelectedCount = useCallback((): number => {
    return selection.selectedRowKeys.length;
  }, [selection.selectedRowKeys.length]);

  // 表格行选择配置
  const rowSelectionAntdTable: TableRowSelection<T> = {
    type: 'checkbox',
    selectedRowKeys: selection.selectedRowKeys,
    onChange: handleSelectionChange,
    onSelect: (record, selected, selectedRows, nativeEvent) => {
      console.log('单行选择:', { record, selected, selectedRows, nativeEvent });
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      console.log('全选/取消全选:', { selected, selectedRows, changeRows });
    },
    getCheckboxProps: record => ({
      name: `checkbox-${record.id}`,
    }),
  };

  // 处理初始选择的keys和外部selectedKeys变化
  useEffect(() => {
    if (initialSelectedKeys.length > 0 && data.length > 0) {
      const selectedRowsFromKeys = data.filter(item => initialSelectedKeys.includes(item.id));

      // 更新当前页面选择状态
      const currentPageSelectedKeys = selectedRowsFromKeys.map(item => item.id);
      setSelection({
        selectedRowKeys: currentPageSelectedKeys,
        selectedRows: selectedRowsFromKeys,
      });
    } else if (initialSelectedKeys.length === 0) {
      // 如果initialSelectedKeys为空，只清空当前页面的选择状态
      setSelection({
        selectedRowKeys: [],
        selectedRows: [],
      });
    }
  }, [initialSelectedKeys, data]);

  // 当数据变化时，更新当前页面的选择状态
  // useEffect(() => {
  //   if (crossPage) {
  //     // 跨页面模式：计算当前页面中哪些行应该被选中
  //     const currentPageSelectedKeys: React.Key[] = [];
  //     const currentPageSelectedRows: T[] = [];

  //     data.forEach(item => {
  //       if (allSelectedRows.has(item.id)) {
  //         currentPageSelectedKeys.push(item.id);
  //         currentPageSelectedRows.push(item);
  //       }
  //     });

  //     setSelection({
  //       selectedRowKeys: currentPageSelectedKeys,
  //       selectedRows: currentPageSelectedRows,
  //     });
  //   } else {
  //     // 非跨页面模式：清空不存在于当前数据中的选择项
  //     const currentDataIds = new Set(data.map(item => item.id));
  //     const validSelectedKeys = selection.selectedRowKeys.filter(key => currentDataIds.has(key));
  //     const validSelectedRows = selection.selectedRows.filter(row => currentDataIds.has(row.id));

  //     if (validSelectedKeys.length !== selection.selectedRowKeys.length) {
  //       setSelection({
  //         selectedRowKeys: validSelectedKeys,
  //         selectedRows: validSelectedRows,
  //       });
  //     }
  //   }
  // }, [data, crossPage, allSelectedRows, selection.selectedRowKeys, selection.selectedRows]);

  return {
    rowSelectionAntdTable,
    clearSelection,
    selectAll,
    unselectAll,
    getSelectedRows,
    getSelectedKeys,
    getSelectedCount,
  };
}
